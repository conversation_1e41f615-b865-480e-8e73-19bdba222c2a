
# --- Required settings even when working locally. ---

# OpenAI API config
OPENAI_MODEL_ID=gpt-4o-mini
OPENAI_API_KEY=

# Huggingface API config
HUGGINGFACE_ACCESS_TOKEN=

# Comet ML (during training and inference) 
COMET_API_KEY=
COMET_WORKSPACE=  # such as your Comet username

# --- Required settings ONLY when using Qdrant Cloud and AWS SageMaker ---

# Qdrant cloud vector database connection config
USE_QDRANT_CLOUD=false
QDRANT_CLOUD_URL=
QDRANT_APIKEY=

# AWS authentication config
AWS_ARN_ROLE=
AWS_REGION=eu-central-1
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
