[supervisord]

[inet_http_server]
port=127.0.0.1:9001

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:uvicorn_host]
command=/opt/pysetup/.venv/bin/python -m uvicorn executor.app.main:app --host 0.0.0.0 --port 8080
numprocs=1
process_name=uvicorn_host
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true

