[tool.poetry]
name = "deployment"
version = "0.2.0"
description = "The Superlinked single node"
authors = ["Superlinked Release <<EMAIL>>"]
license = "Proprietary"
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10.3,<=3.12.3"
pandas = "^2.0.3"

[tool.poetry.group.poller]
optional = true

[tool.poetry.group.poller.dependencies]
boto3 = "^1.34.13"
google-auth = "^2.26.1"
google-cloud-storage = "^2.14.0"
requests = "^2.27.1"
cerberus = "^1.3.5"
pyyaml = "^6.0.1"
chardet = "^5.2.0"
boto3-stubs = {version = "^1.34.13", extras = ["s3"]}

[tool.poetry.group.executor]
optional = true

[tool.poetry.group.executor.dependencies]
fastapi = "^0.109.0"
uvicorn = "^0.15.0"
deltalake = "^0.15.1"
fsspec = "^2023.12.2"
gcsfs = "^2023.12.2.post1"
s3fs = "^2023.12.2"
adlfs = "^2023.12.0"
httpx = "^0.26.0"
inject = "^5.2.0"
fastapi-restful = "^0.5.0"
pydantic = "^2.6.3"
pydantic-settings = "^2.2.1"
typing-inspect = "^0.9.0"
tqdm = "^4.66.3" # Dependency of superlinked. Here because of security vulnerability [FAI-1841]
superlinked = {version = "^6.10.0", extras = ["redis", "mongo"]}

[tool.poetry.group.dev.dependencies]
typing-extensions = "^4.9.0"
pytest = "^7.4.3"
pytest-mock = "^3.12.0"
pytest-emoji = "^0.2.0"
pytest-md = "^0.2.0"
pytest-asyncio = "^0.23.6"
moto = {version = "^5.0.6", extras = ["all"]}
jinja2 = "^3.1.4" # Dependency of moto. Here because of security vulnerability [FAI-1841]
werkzeug = "^3.0.3" # Dependency of moto. Here because of security vulnerability [FAI-1841]
types-google-cloud-ndb = "^2.2.0.20240205"
types-jmespath = "^1.0.2.20240106"
types-requests = "^2.27.1"
types-pyyaml = "^6.0.12.12"
mypy-boto3 = "^1.34.52"
mypy = "<1.6"
typing-protocol-intersection = "^0.4.0"
ruff = "^0.4.2"
pylint = "^3.1.0"
vermin = "^1.6.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
implicit_optional = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
plugins = ["pydantic.mypy", "typing_protocol_intersection.mypy_plugin"]
exclude = "(.*/)?test(/|$)"

[[tool.mypy.overrides]]
module = "cerberus.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "google.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "uvicorn.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "fsspec.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "pandas.*"
ignore_missing_imports = true

[tool.ruff]
exclude = [
    ".git",
    ".mypy_cache",
    ".pytest_cache",
    ".ruff_cache",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "build",
    "dist",
]
extend-exclude = ["test"]
line-length = 120
indent-width = 4
target-version = "py310"

[tool.ruff.lint]
select = ["ALL"]
ignore = [
    "D1",
    "D2",
    "PTH",
    "N818",
    "ANN101",
    "TD002",
    "TD003",
    "FIX002",
]
# These rules need to be ignore because of the formatter
extend-ignore = ["ISC001", "COM812"]
fixable = ["ALL"]
unfixable = []

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = false
docstring-code-line-length = "dynamic"

[tool.pylint.MASTER]
max-args = 6
max-line-length = 120
ignore = 'test'
disable = [
    "missing-module-docstring",
    "missing-class-docstring",
    "missing-function-docstring",
    "too-few-public-methods",
    "protected-access",
    "fixme",
]
