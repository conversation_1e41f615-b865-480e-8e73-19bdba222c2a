[tool.poetry]
name = "llm-twin-course"
description = ""
version = "0.1.0"
authors = [
    "<PERSON> <p.b<PERSON><PERSON>@gmail.com>",
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON><PERSON>ru <<EMAIL>>",
    "<PERSON><PERSON> Istoc <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>"
]
package-mode = false
readme = "README.md"

[tool.ruff]
line-length = 88
select = [
    "F401",
    "F403",
]

[tool.poetry.dependencies]
python = "~3.11"
pydantic = "^2.6.3"
pydantic-settings = "^2.2.0"
pika = "^1.3.2"
qdrant-client = "^1.8.0"
aws-lambda-powertools = "^2.38.1"
selenium = "4.21.0"
instructorembedding = "^1.0.1"
numpy = "^1.26.4"
gdown = "^5.1.0"
pymongo = "^4.7.1"
structlog = "^24.1.0"
rich = "^13.7.1"
comet-ml = "^3.41.0"
opik = "1.0.1"
ruff = "^0.4.3"
pandas = "^2.0.3"
datasets = "^2.19.1"
scikit-learn = "^1.4.2" 
unstructured = "^0.14.2"
litellm = "^1.50.4"
langchain = "^0.2.11"
langchain-openai = "^0.1.3"
langchain-community = "^0.2.11"
html2text = "^2024.2.26"
huggingface-hub = "0.25.1"
sagemaker = ">=2.232.2"
sentence-transformers = "^2.2.2"
gradio = "^5.5.0"

[tool.poetry.group.feature_pipeline.dependencies]
bytewax = "0.18.2"

[tool.poetry.group.superlinked_rag.dependencies]
superlinked = "^7.2.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
